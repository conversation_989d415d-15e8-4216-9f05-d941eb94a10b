{"name": "unit-converter-hub", "version": "1.0.0", "description": "A comprehensive unit converter application for students, professionals, and travelers with real-time conversions and responsive design", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "build": "npm run generate-assets", "generate-assets": "node scripts/generate-assets.js"}, "keywords": ["unit-converter", "conversion", "calculator", "measurement", "responsive", "mobile"], "author": "<PERSON><PERSON> (chirag127)", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "sharp": "^0.33.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/chirag127/Unit-Converter-Hub.git"}, "bugs": {"url": "https://github.com/chirag127/Unit-Converter-Hub/issues"}, "homepage": "https://github.com/chirag127/Unit-Converter-Hub#readme"}